import Link from "next/link"
import { FileText } from "lucide-react"

export function Header() {
  return (
    <header className="border-b border-gray-800 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <nav className="mx-auto flex h-14 max-w-8xl items-center justify-between px-4 sm:px-6 lg:px-8">
        <div className="flex items-center space-x-4">
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded bg-green-600 flex items-center justify-center">
              <span className="text-white font-bold text-sm">S</span>
            </div>
            <span className="hidden font-bold sm:inline-block text-white">
              Supabase
            </span>
          </Link>
        </div>
        
        <div className="flex items-center space-x-4">
          <Link
            href="/docs"
            className="flex items-center space-x-1 text-sm text-gray-400 hover:text-white transition-colors"
          >
            <FileText className="h-4 w-4" />
            <span>Documentation</span>
          </Link>
        </div>
      </nav>
    </header>
  )
}
