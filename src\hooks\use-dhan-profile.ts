/**
 * Custom hook for fetching and managing Dhan profile data
 */

import { useState, useEffect, useCallback } from 'react';
import { UseDhanProfileState, ProcessedProfileData } from '@/types/dhan';

interface DhanProfileApiResponse {
  success: boolean;
  data?: ProcessedProfileData;
  error?: string;
  details?: any;
}

export function useDhanProfile(): UseDhanProfileState {
  const [data, setData] = useState<ProcessedProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/dhan/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result: DhanProfileApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP error! status: ${response.status}`);
      }

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to fetch profile data');
      }

      setData(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching Dhan profile:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    await fetchProfile();
  }, [fetchProfile]);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    data,
    loading,
    error,
    refetch,
  };
}
