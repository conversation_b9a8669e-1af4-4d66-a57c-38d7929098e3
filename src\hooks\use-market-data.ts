/**
 * Enhanced Custom hook for fetching and managing live market data
 * Supports WebSocket real-time data, historical data fallback, and market hours detection
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { UseMarketDataState, ProcessedMarketData } from '@/types/dhan';
import { getCombinedMarketStatus, getMarketStatus, MarketStatus } from '@/lib/market-hours';
import {
  getLastAvailableData,
  saveMarketDataCache,
  getCacheMetadata,
  HistoricalMarketData
} from '@/lib/historical-data-storage';
import {
  WebSocketMarketDataService,
  DEFAULT_WEBSOCKET_CONFIG,
  WebSocketConfig
} from '@/lib/websocket-market-data';

interface MarketDataApiResponse {
  success: boolean;
  data?: ProcessedMarketData[];
  error?: string;
  details?: any;
  timestamp?: string;
}

export type DataSource = 'LIVE' | 'HISTORICAL' | 'DEMO' | 'WEBSOCKET';

export interface MarketDataStatus {
  dataSource: DataSource;
  isLive: boolean;
  lastUpdate: Date | null;
  marketStatus: {
    nse: MarketStatus;
    mcx: MarketStatus;
    anyOpen: boolean;
    allClosed: boolean;
  };
  websocketStatus?: {
    isConnected: boolean;
    isConnecting: boolean;
    reconnectAttempts: number;
  };
  cacheInfo?: {
    hasCache: boolean;
    isFromToday: boolean;
    ageInHours: number;
  };
}

interface UseMarketDataOptions {
  refreshInterval?: number; // in milliseconds, default 60 seconds (increased to avoid rate limits)
  autoRefresh?: boolean; // default true
  enableWebSocket?: boolean; // default true when markets are open
  fallbackToHistorical?: boolean; // default true
}

export function useMarketData(options: UseMarketDataOptions = {}): UseMarketDataState & { status: MarketDataStatus } {
  const {
    refreshInterval = 60000,
    autoRefresh = true,
    enableWebSocket = true,
    fallbackToHistorical = true
  } = options;

  const [data, setData] = useState<ProcessedMarketData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [dataSource, setDataSource] = useState<DataSource>('DEMO');
  const [marketStatus, setMarketStatus] = useState(getCombinedMarketStatus());

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const retryCountRef = useRef(0);
  const lastErrorRef = useRef<string | null>(null);
  const wsServiceRef = useRef<WebSocketMarketDataService | null>(null);
  const marketStatusIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Load historical data on mount
  const loadHistoricalData = useCallback(() => {
    const historicalData = getLastAvailableData();
    if (historicalData && historicalData.length > 0) {
      console.log('Loading historical market data:', historicalData.length, 'instruments');
      setData(historicalData);
      setDataSource('HISTORICAL');
      setLastUpdated(new Date(historicalData[0].capturedAt));
      setLoading(false);
      return true;
    }
    return false;
  }, []);

  const fetchMarketData = useCallback(async (forceRefresh = false) => {
    try {
      setError(null);
      if (forceRefresh) setLoading(true);

      const response = await fetch('/api/dhan/market-data', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result: MarketDataApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP error! status: ${response.status}`);
      }

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to fetch market data');
      }

      if (mountedRef.current && result.data.length > 0) {
        setData(result.data);
        setDataSource('LIVE');
        setLastUpdated(new Date());
        retryCountRef.current = 0;
        lastErrorRef.current = null;

        // Cache the live data
        saveMarketDataCache(result.data, 'API');
      } else if (mountedRef.current && result.data.length === 0) {
        // API returned empty data, try to load historical data
        if (fallbackToHistorical && !loadHistoricalData()) {
          // No historical data available, show demo data
          setDataSource('DEMO');
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';

      if (mountedRef.current) {
        // Handle different types of errors
        if (errorMessage.toLowerCase().includes('rate limit')) {
          retryCountRef.current += 1;
          lastErrorRef.current = errorMessage;

          // Try to load historical data for rate limits
          if (fallbackToHistorical && !loadHistoricalData()) {
            setError(errorMessage);
            setDataSource('DEMO');
          }
        } else {
          // For other errors, try historical data first
          if (fallbackToHistorical && !loadHistoricalData()) {
            setError(errorMessage);
            setDataSource('DEMO');
          }
        }
      }

      console.error('Error fetching market data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // WebSocket management
  const initializeWebSocket = useCallback(async () => {
    if (!enableWebSocket || wsServiceRef.current) return;

    try {
      // This would need actual WebSocket URL and credentials from Dhan API
      // For now, we'll simulate WebSocket functionality
      const config: WebSocketConfig = {
        url: 'wss://api.dhan.co/v2/websocket', // Placeholder URL
        accessToken: process.env.NEXT_PUBLIC_DHAN_ACCESS_TOKEN || '',
        clientId: process.env.NEXT_PUBLIC_DHAN_CLIENT_ID || '',
        ...DEFAULT_WEBSOCKET_CONFIG
      };

      const wsService = new WebSocketMarketDataService(config);

      wsService.onData((marketData: ProcessedMarketData) => {
        if (mountedRef.current) {
          setData(prevData => {
            const updatedData = [...prevData];
            const index = updatedData.findIndex(item => item.symbol === marketData.symbol);
            if (index >= 0) {
              updatedData[index] = marketData;
            } else {
              updatedData.push(marketData);
            }
            return updatedData;
          });
          setDataSource('WEBSOCKET');
          setLastUpdated(new Date());
        }
      });

      wsService.onConnected(() => {
        console.log('WebSocket connected, subscribing to instruments');
        wsService.subscribe(['NIFTY50', 'SILVER']); // Subscribe to our instruments
      });

      wsService.onError((error) => {
        console.error('WebSocket error:', error);
        // Fallback to REST API polling
        if (autoRefresh) {
          scheduleNextRefresh();
        }
      });

      const connected = await wsService.connect();
      if (connected) {
        wsServiceRef.current = wsService;
        console.log('WebSocket service initialized');
      }
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
    }
  }, [enableWebSocket, autoRefresh]);

  const refetch = useCallback(async () => {
    setLoading(true);
    setError(null);
    await fetchMarketData(true);
  }, [fetchMarketData]);

  // Market status monitoring
  const updateMarketStatus = useCallback(() => {
    const newStatus = getCombinedMarketStatus();
    setMarketStatus(newStatus);

    // Smart data source switching based on market status
    if (newStatus.anyOpen && enableWebSocket && !wsServiceRef.current) {
      // Markets are open, initialize WebSocket
      initializeWebSocket();
    } else if (!newStatus.anyOpen && wsServiceRef.current) {
      // Markets are closed, disconnect WebSocket and load historical data
      wsServiceRef.current.disconnect();
      wsServiceRef.current = null;

      if (fallbackToHistorical) {
        loadHistoricalData();
      }
    }
  }, [enableWebSocket, initializeWebSocket, fallbackToHistorical, loadHistoricalData]);

  // Initial setup
  useEffect(() => {
    // Load historical data first (immediate display)
    if (fallbackToHistorical) {
      const hasHistoricalData = loadHistoricalData();
      if (!hasHistoricalData) {
        setDataSource('DEMO');
        setLoading(false);
      }
    } else {
      setDataSource('DEMO');
      setLoading(false);
    }

    // Update market status
    updateMarketStatus();

    // Try to fetch live data
    fetchMarketData();
  }, [fetchMarketData, loadHistoricalData, updateMarketStatus, fallbackToHistorical]);

  // Schedule next refresh (used by both auto-refresh and error recovery)
  const scheduleNextRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearTimeout(intervalRef.current);
    }

    let delay = refreshInterval;

    // Apply exponential backoff for rate limit errors
    if (lastErrorRef.current?.toLowerCase().includes('rate limit')) {
      delay = Math.min(refreshInterval * Math.pow(2, retryCountRef.current), 300000); // Max 5 minutes
    }

    intervalRef.current = setTimeout(() => {
      // Only use REST API polling if WebSocket is not connected
      if (!wsServiceRef.current?.getStatus().isConnected) {
        fetchMarketData();
      }
      scheduleNextRefresh();
    }, delay);
  }, [fetchMarketData, refreshInterval]);

  // Set up auto-refresh and market status monitoring
  useEffect(() => {
    if (!autoRefresh) return;

    // Start auto-refresh
    scheduleNextRefresh();

    // Monitor market status every minute
    marketStatusIntervalRef.current = setInterval(updateMarketStatus, 60000);

    return () => {
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
      if (marketStatusIntervalRef.current) {
        clearInterval(marketStatusIntervalRef.current);
      }
    };
  }, [autoRefresh, scheduleNextRefresh, updateMarketStatus]);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;

      // Cleanup timers
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
      if (marketStatusIntervalRef.current) {
        clearInterval(marketStatusIntervalRef.current);
      }

      // Cleanup WebSocket
      if (wsServiceRef.current) {
        wsServiceRef.current.disconnect();
        wsServiceRef.current = null;
      }
    };
  }, []);

  // Create status object
  const status: MarketDataStatus = {
    dataSource,
    isLive: dataSource === 'WEBSOCKET' || (dataSource === 'LIVE' && marketStatus.anyOpen),
    lastUpdate: lastUpdated,
    marketStatus,
    websocketStatus: wsServiceRef.current?.getStatus(),
    cacheInfo: getCacheMetadata()
  };

  return {
    data,
    loading,
    error,
    lastUpdated,
    refetch,
    status,
  };
}

/**
 * Hook for fetching market data with custom refresh control and enhanced features
 */
export function useMarketDataWithControl(refreshInterval: number = 60000) {
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(true);
  const [enableWebSocket, setEnableWebSocket] = useState(true);

  const marketData = useMarketData({
    refreshInterval,
    autoRefresh: isAutoRefreshEnabled,
    enableWebSocket,
    fallbackToHistorical: true,
  });

  const toggleAutoRefresh = useCallback(() => {
    setIsAutoRefreshEnabled(prev => !prev);
  }, []);

  const toggleWebSocket = useCallback(() => {
    setEnableWebSocket(prev => !prev);
  }, []);

  const setRefreshInterval = useCallback((interval: number) => {
    // This would require a more complex implementation to dynamically change interval
    // For now, we'll just return the current state
    return marketData;
  }, [marketData]);

  return {
    ...marketData,
    isAutoRefreshEnabled,
    enableWebSocket,
    toggleAutoRefresh,
    toggleWebSocket,
    setRefreshInterval,
  };
}
