/**
 * WebSocket Market Data Service
 * Handles real-time market data connections with authentication and reconnection logic
 */

import { ProcessedMarketData } from '@/types/dhan';
import { updateInstrumentInCache } from './historical-data-storage';

export interface WebSocketConfig {
  url: string;
  accessToken: string;
  clientId: string;
  reconnectInterval: number; // milliseconds
  maxReconnectAttempts: number;
  heartbeatInterval: number; // milliseconds
}

export interface WebSocketMessage {
  type: 'SUBSCRIBE' | 'UNSUBSCRIBE' | 'HEARTBEAT' | 'DATA' | 'ERROR' | 'AUTH';
  data?: any;
  timestamp?: string;
}

export interface MarketDataUpdate {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume?: number;
  timestamp: string;
}

export type WebSocketEventHandler = (data: any) => void;

export class WebSocketMarketDataService {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private isConnected = false;
  private isConnecting = false;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private subscribedInstruments: Set<string> = new Set();
  
  // Event handlers
  private onDataHandler: WebSocketEventHandler | null = null;
  private onConnectedHandler: (() => void) | null = null;
  private onDisconnectedHandler: (() => void) | null = null;
  private onErrorHandler: ((error: any) => void) | null = null;

  constructor(config: WebSocketConfig) {
    this.config = config;
  }

  /**
   * Connect to WebSocket server
   */
  async connect(): Promise<boolean> {
    if (this.isConnected || this.isConnecting) {
      return this.isConnected;
    }

    this.isConnecting = true;
    
    try {
      console.log('Connecting to WebSocket:', this.config.url);
      
      this.ws = new WebSocket(this.config.url);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

      // Wait for connection to establish
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          this.isConnecting = false;
          resolve(false);
        }, 10000); // 10 second timeout

        const originalOnOpen = this.ws!.onopen;
        this.ws!.onopen = (event) => {
          clearTimeout(timeout);
          originalOnOpen?.call(this.ws, event);
          resolve(this.isConnected);
        };
      });
    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
      return false;
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    console.log('Disconnecting WebSocket');
    
    this.clearTimers();
    this.subscribedInstruments.clear();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Subscribe to instrument updates
   */
  subscribe(instruments: string[]): boolean {
    if (!this.isConnected) {
      console.warn('WebSocket not connected, cannot subscribe');
      return false;
    }

    const message: WebSocketMessage = {
      type: 'SUBSCRIBE',
      data: {
        instruments,
        mode: 'FULL' // Request full market data
      },
      timestamp: new Date().toISOString()
    };

    try {
      this.ws!.send(JSON.stringify(message));
      instruments.forEach(instrument => this.subscribedInstruments.add(instrument));
      console.log('Subscribed to instruments:', instruments);
      return true;
    } catch (error) {
      console.error('Error subscribing to instruments:', error);
      return false;
    }
  }

  /**
   * Unsubscribe from instrument updates
   */
  unsubscribe(instruments: string[]): boolean {
    if (!this.isConnected) {
      return false;
    }

    const message: WebSocketMessage = {
      type: 'UNSUBSCRIBE',
      data: { instruments },
      timestamp: new Date().toISOString()
    };

    try {
      this.ws!.send(JSON.stringify(message));
      instruments.forEach(instrument => this.subscribedInstruments.delete(instrument));
      console.log('Unsubscribed from instruments:', instruments);
      return true;
    } catch (error) {
      console.error('Error unsubscribing from instruments:', error);
      return false;
    }
  }

  /**
   * Set event handlers
   */
  onData(handler: WebSocketEventHandler): void {
    this.onDataHandler = handler;
  }

  onConnected(handler: () => void): void {
    this.onConnectedHandler = handler;
  }

  onDisconnected(handler: () => void): void {
    this.onDisconnectedHandler = handler;
  }

  onError(handler: (error: any) => void): void {
    this.onErrorHandler = handler;
  }

  /**
   * Get connection status
   */
  getStatus(): {
    isConnected: boolean;
    isConnecting: boolean;
    reconnectAttempts: number;
    subscribedCount: number;
  } {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      subscribedCount: this.subscribedInstruments.size
    };
  }

  // Private methods

  private handleOpen(event: Event): void {
    console.log('WebSocket connected');
    this.isConnected = true;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // Authenticate
    this.authenticate();
    
    // Start heartbeat
    this.startHeartbeat();
    
    this.onConnectedHandler?.();
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'DATA':
          this.handleMarketData(message.data);
          break;
        case 'HEARTBEAT':
          // Heartbeat response - connection is alive
          break;
        case 'ERROR':
          console.error('WebSocket server error:', message.data);
          this.onErrorHandler?.(message.data);
          break;
        default:
          console.log('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('WebSocket disconnected:', event.code, event.reason);
    this.isConnected = false;
    this.isConnecting = false;
    
    this.clearTimers();
    this.onDisconnectedHandler?.();
    
    // Attempt reconnection if not a clean close
    if (event.code !== 1000 && this.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.onErrorHandler?.(event);
  }

  private authenticate(): void {
    const authMessage: WebSocketMessage = {
      type: 'AUTH',
      data: {
        accessToken: this.config.accessToken,
        clientId: this.config.clientId
      },
      timestamp: new Date().toISOString()
    };

    try {
      this.ws!.send(JSON.stringify(authMessage));
      console.log('Authentication message sent');
    } catch (error) {
      console.error('Error sending authentication:', error);
    }
  }

  private handleMarketData(data: any): void {
    try {
      // Process market data update
      const processedData = this.processMarketDataUpdate(data);
      
      if (processedData) {
        // Update cache with real-time data
        updateInstrumentInCache(processedData, 'WEBSOCKET');
        
        // Notify handlers
        this.onDataHandler?.(processedData);
      }
    } catch (error) {
      console.error('Error processing market data:', error);
    }
  }

  private processMarketDataUpdate(rawData: any): ProcessedMarketData | null {
    try {
      // This would need to be adapted based on the actual Dhan WebSocket API format
      // For now, this is a placeholder implementation
      return {
        symbol: rawData.symbol || rawData.tradingSymbol,
        name: rawData.name || rawData.symbol,
        currentPrice: rawData.ltp || rawData.lastPrice,
        change: rawData.change || (rawData.ltp - rawData.close),
        changePercent: rawData.changePercent || ((rawData.ltp - rawData.close) / rawData.close * 100),
        isPositive: (rawData.change || (rawData.ltp - rawData.close)) >= 0,
        open: rawData.open,
        high: rawData.high,
        low: rawData.low,
        close: rawData.close,
        volume: rawData.volume,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error processing market data update:', error);
      return null;
    }
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected && this.ws) {
        const heartbeat: WebSocketMessage = {
          type: 'HEARTBEAT',
          timestamp: new Date().toISOString()
        };
        
        try {
          this.ws.send(JSON.stringify(heartbeat));
        } catch (error) {
          console.error('Error sending heartbeat:', error);
        }
      }
    }, this.config.heartbeatInterval);
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = Math.min(this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimer = setTimeout(() => {
      this.connect().then(connected => {
        if (connected && this.subscribedInstruments.size > 0) {
          // Re-subscribe to instruments
          this.subscribe(Array.from(this.subscribedInstruments));
        }
      });
    }, delay);
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
}

// Default WebSocket configuration
export const DEFAULT_WEBSOCKET_CONFIG: Partial<WebSocketConfig> = {
  reconnectInterval: 5000, // 5 seconds
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000 // 30 seconds
};
